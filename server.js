import express from 'express'
import multer from 'multer'
import path from 'path'
import fs from 'fs/promises'
import cors from 'cors'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = 3000

// Enable CORS for all origins (for local development)
app.use(cors())
app.use(express.json())

// Storage configuration
const STORAGE_CONFIG = {
  baseDir: 'led-fans-storage',
  directories: {
    materials: {
      images: 'files/materials/images',
      videos: 'files/materials/videos',
      music: 'files/materials/music'
    },
    compositions: 'files/media-compositions',
    metadata: 'metadata'
  }
}

// Create directories if they don't exist
async function initializeDirectories() {
  const dirs = [
    STORAGE_CONFIG.directories.materials.images,
    STORAGE_CONFIG.directories.materials.videos,
    STORAGE_CONFIG.directories.materials.music,
    STORAGE_CONFIG.directories.compositions,
    STORAGE_CONFIG.directories.metadata
  ]
  
  for (const dir of dirs) {
    const fullPath = path.join(STORAGE_CONFIG.baseDir, dir)
    try {
      await fs.mkdir(fullPath, { recursive: true })
      console.log(`Created directory: ${fullPath}`)
    } catch (error) {
      console.error(`Error creating directory ${fullPath}:`, error)
    }
  }
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    // Since req.body might not be parsed yet when destination is called,
    // we'll use the file mimetype to determine the correct directory
    let subDir = ''
    if (file.mimetype.startsWith('image/')) {
      subDir = STORAGE_CONFIG.directories.materials.images
    } else if (file.mimetype.startsWith('video/')) {
      subDir = STORAGE_CONFIG.directories.materials.videos
    } else if (file.mimetype.startsWith('audio/')) {
      subDir = STORAGE_CONFIG.directories.materials.music
    } else {
      return cb(new Error('Unsupported file type'))
    }
    
    const fullPath = path.join(STORAGE_CONFIG.baseDir, subDir)
    try {
      await fs.mkdir(fullPath, { recursive: true })
      cb(null, fullPath)
    } catch (error) {
      cb(error)
    }
  },
  filename: (req, file, cb) => {
    // Generate unique filename to avoid conflicts
    const timestamp = Date.now()
    const randomSuffix = Math.random().toString(36).substring(2, 8)
    const extension = path.extname(file.originalname)
    const uniqueName = `${timestamp}_${randomSuffix}${extension}`
    cb(null, uniqueName)
  }
})

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  }
})

// Serve static files
app.use('/files', express.static(STORAGE_CONFIG.baseDir))

// Serve frontend static files
app.use(express.static('dist'))

// File upload endpoint
app.post('/api/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' })
    }

    // Get the relative path from the storage base directory
    const relativePath = path.relative(STORAGE_CONFIG.baseDir, req.file.path)
    const fileUrl = `/files/${relativePath.replace(/\\/g, '/')}`
    
    console.log(`File uploaded: ${req.file.originalname} -> ${req.file.path}`)
    console.log(`Relative path: ${relativePath}`)
    console.log(`File URL: ${fileUrl}`)
    
    res.json({
      success: true,
      path: req.file.path, // Full path for backend reference
      url: fileUrl, // URL for frontend access
      size: req.file.size,
      originalName: req.file.originalname
    })
  } catch (error) {
    console.error('Upload error:', error)
    res.status(500).json({ error: 'Upload failed' })
  }
})

// File deletion endpoint
app.delete('/api/delete', async (req, res) => {
  try {
    const { path: filePath } = req.body
    if (!filePath) {
      return res.status(400).json({ error: 'No file path provided' })
    }

    // Convert URL path to file system path
    let relativePath = filePath
    // Remove /files/ prefix if present (from URL format)
    if (relativePath.startsWith('/files/')) {
      relativePath = relativePath.substring(7) // Remove '/files/'
    }
    
    // Construct full path within storage directory
    const fullPath = path.join(STORAGE_CONFIG.baseDir, relativePath)
    
    // Security check: ensure path is within storage directory
    const resolvedPath = path.resolve(fullPath)
    const storageBasePath = path.resolve(STORAGE_CONFIG.baseDir)
    if (!resolvedPath.startsWith(storageBasePath)) {
      return res.status(403).json({ error: 'Access denied: path outside storage directory' })
    }

    await fs.unlink(resolvedPath)
    
    console.log(`File deleted: ${resolvedPath}`)
    res.json({ success: true, path: resolvedPath })
  } catch (error) {
    console.error('Delete error:', error)
    if (error.code === 'ENOENT') {
      res.status(404).json({ error: 'File not found' })
    } else {
      res.status(500).json({ error: 'Delete failed' })
    }
  }
})

// Metadata endpoints
app.get('/api/metadata/:fileName', async (req, res) => {
  try {
    const { fileName } = req.params
    const metadataPath = path.join(STORAGE_CONFIG.baseDir, STORAGE_CONFIG.directories.metadata, fileName)
    
    const data = await fs.readFile(metadataPath, 'utf8')
    res.json(JSON.parse(data))
  } catch (error) {
    if (error.code === 'ENOENT') {
      res.status(404).json({ error: 'Metadata not found' })
    } else {
      console.error('Metadata read error:', error)
      res.status(500).json({ error: 'Failed to read metadata' })
    }
  }
})

app.post('/api/metadata', async (req, res) => {
  try {
    const { fileName, data } = req.body
    if (!fileName || !data) {
      return res.status(400).json({ error: 'Missing fileName or data' })
    }

    const metadataPath = path.join(STORAGE_CONFIG.baseDir, STORAGE_CONFIG.directories.metadata, fileName)
    await fs.writeFile(metadataPath, JSON.stringify(data, null, 2))
    
    console.log(`Metadata saved: ${fileName}`)
    res.json({ success: true })
  } catch (error) {
    console.error('Metadata save error:', error)
    res.status(500).json({ error: 'Failed to save metadata' })
  }
})

// Default route - serve frontend
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'))
})

// Start server
async function startServer() {
  try {
    await initializeDirectories()
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 Server running at http://0.0.0.0:${PORT}`)
      console.log(`📁 Storage directory: ${STORAGE_CONFIG.baseDir}`)
    })
  } catch (error) {
    console.error('Failed to start server:', error)
  }
}

startServer() 