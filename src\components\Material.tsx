import React, { useState } from 'react'
import type { UploadFile, UploadProps } from 'antd'
import { App, Upload, Button } from 'antd'
import {
  DeleteOutlined,
  RightOutlined
} from '@ant-design/icons'
import { SmallVideoIcon, SmallImageIcon, SmallMusicIcon, VideoIcon, MusicIcon } from '../assets/icons'
import { realDataStorage, formatFileSize } from '../data/storage'
import '../styles/material.css'

const { Dragger } = Upload
const { useApp } = App

// File icon mapping function
const getFileIcon = (fileType: string) => {
  const type = fileType?.toLowerCase() || ''
  if (type.startsWith('image/')) {
    return <SmallImageIcon color="#52c41a" />
  }
  if (type.startsWith('video/')) {
    return <SmallVideoIcon color="#c27aff" />
  }
  if (type.startsWith('audio/')) {
    return <SmallMusicIcon color="#fa8c16" />
  }
  return <SmallImageIcon color="#ff2056" />
}

// Truncate middle function
const truncateMiddle = (str: string, startLen = 20, endLen = 8) => {
  if (str.length <= startLen + endLen) return str
  return `${str.slice(0, startLen)}...${str.slice(-endLen)}`
}

// File item component for pending files
const PendingFileItem = React.memo(({ file, onRemove }: {
  file: UploadFile,
  onRemove: (file: UploadFile) => void
}) => {
  return (
    <div className="file-item-modern">
      <div className="file-item-info">
        <span className="file-icon">{getFileIcon(file.type || '')}</span>
        <div className="file-details">
          <div className="file-name">{truncateMiddle(file.name, 30)}</div>
          <div className="file-meta">
            <span className="file-type">{(file.type || '').split('/')[1]?.toUpperCase() || 'FILE'}</span>
            <span className="separator">•</span>
            <span className="file-size">{formatFileSize(file.size || 0)}</span>
            <span className="separator">•</span>
            <span className="file-status">Pending</span>
          </div>
        </div>
      </div>
      <div className="file-actions">
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => onRemove(file)}
          size="small"
          className="delete-button"
          title="Remove"
        />
      </div>
    </div>
  )
})

// File group component for pending files
const PendingFileGroup = React.memo(({
  title,
  files,
  onRemove,
  defaultExpanded = true
}: {
  title: string,
  files: UploadFile[],
  onRemove: (file: UploadFile) => void,
  defaultExpanded?: boolean
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0)

  if (files.length === 0) return null

  return (
    <div className="file-group">
      <div
        className="file-group-header"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="file-group-title-wrapper">
          <RightOutlined className={`file-group-arrow ${isExpanded ? 'expanded' : ''}`} />
          <div className="file-group-title">{title} (Pending Upload)</div>
        </div>
        <div className="file-group-summary">
          <span className="file-count">
            {files.length} files, {formatFileSize(totalSize)}
          </span>
        </div>
      </div>
      <div className={`file-group-content ${isExpanded ? 'expanded' : ''}`}>
        {files.map((file, index) => (
          <PendingFileItem
            key={`${file.name}-${index}`}
            file={file}
            onRemove={onRemove}
          />
        ))}
      </div>
    </div>
  )
})

const Material: React.FC = () => {
  const { message, modal } = useApp()
  const [uploading, setUploading] = useState(false)
  
  // Pending upload files
  const [imageVideoFileList, setImageVideoFileList] = useState<UploadFile[]>([])
  const [audioFileList, setAudioFileList] = useState<UploadFile[]>([])

  // Handle batch upload
  const handleUpload = async () => {
    const allFiles = [...imageVideoFileList, ...audioFileList]
    if (allFiles.length === 0) {
      message.warning('Please select files to upload.')
      return
    }

    setUploading(true)
    let successCount = 0
    let errorCount = 0

    try {
      for (const uploadFile of allFiles) {
        try {
          if (uploadFile.originFileObj) {
            await realDataStorage.addMaterialFile(uploadFile.originFileObj)
            successCount++
          } else {
            console.error(`No originFileObj for file: ${uploadFile.name}`)
            errorCount++
          }
        } catch (error) {
          console.error(`Error uploading ${uploadFile.name}:`, error)
          errorCount++
        }
      }

      // Clear pending files after upload
      setImageVideoFileList([])
      setAudioFileList([])

      if (errorCount === 0) {
        message.success(`All ${successCount} file(s) uploaded successfully.`)
      } else {
        message.warning(`${successCount} file(s) uploaded successfully, ${errorCount} failed.`)
      }
    } finally {
      setUploading(false)
    }
  }

  // Handle removing files from pending list
  const handleImageVideoFileRemove = (file: UploadFile) => {
    const index = imageVideoFileList.indexOf(file)
    const newFileList = imageVideoFileList.slice()
    newFileList.splice(index, 1)
    setImageVideoFileList(newFileList)
  }

  const handleAudioFileRemove = (file: UploadFile) => {
    const index = audioFileList.indexOf(file)
    const newFileList = audioFileList.slice()
    newFileList.splice(index, 1)
    setAudioFileList(newFileList)
  }

  // Clear all pending files
  const handleClearPending = () => {
    const totalPending = imageVideoFileList.length + audioFileList.length
    if (totalPending === 0) {
      message.info('No pending files to clear.')
      return
    }

    modal.confirm({
      title: 'Clear Pending Files',
      content: `Are you sure you want to clear all ${totalPending} pending file(s)?`,
      okText: 'Clear',
      centered: true,
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        setImageVideoFileList([])
        setAudioFileList([])
        message.success('All pending files cleared.')
      },
    })
  }

  // Upload props for images and videos
  const imageVideoUploadProps: UploadProps = {
    accept: '.jpeg,.jpg,.png,.mp4,.mov',
    multiple: true,
    onRemove: handleImageVideoFileRemove,
    beforeUpload: (file) => {
      // Strict type check
      const allowedTypes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'video/mp4',
        'video/quicktime'
      ]
      const isAllowedType = allowedTypes.includes(file.type || '')
      if (!isAllowedType) {
        message.error(`${file.name} is not a supported file type. Only .jpeg, .jpg, .png, .mp4, .mov files are allowed.`)
        return Upload.LIST_IGNORE
      }

      // Create upload file with originFileObj
      const uploadFile: UploadFile = {
        uid: `${Date.now()}-${Math.random()}`,
        name: file.name,
        type: file.type,
        size: file.size,
        originFileObj: file,
        status: 'done'
      }
      
      // Use functional update to avoid closure issues with multiple files
      setImageVideoFileList(prevList => [...prevList, uploadFile])
      return false // Prevent automatic upload
    },
    fileList: imageVideoFileList,
    showUploadList: false,
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files)
    },
  }

  // Upload props for audio
  const audioUploadProps: UploadProps = {
    accept: '.mp3,.m4a,.wav',
    multiple: true, // Allow multiple audio files
    onRemove: handleAudioFileRemove,
    beforeUpload: (file) => {
      // Strict type check
      const allowedTypes = [
        'audio/mpeg',
        'audio/mp3',
        'audio/mp4',
        'audio/x-m4a',
        'audio/wav',
        'audio/wave'
      ]
      const isAllowedType = allowedTypes.includes(file.type || '')
      if (!isAllowedType) {
        message.error(`${file.name} is not a supported audio file type. Only .mp3, .m4a, .wav files are allowed.`)
        return Upload.LIST_IGNORE
      }

      // Create upload file with originFileObj
      const uploadFile: UploadFile = {
        uid: `${Date.now()}-${Math.random()}`,
        name: file.name,
        type: file.type,
        size: file.size,
        originFileObj: file,
        status: 'done'
      }

      // Use functional update to avoid closure issues with multiple files
      setAudioFileList(prevList => [...prevList, uploadFile])
      return false // Prevent automatic upload
    },
    fileList: audioFileList,
    showUploadList: false,
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files)
    },
  }

  // Separate pending files by type
  const pendingImageFiles = imageVideoFileList.filter(file => file.type?.startsWith('image/'))
  const pendingVideoFiles = imageVideoFileList.filter(file => file.type?.startsWith('video/'))

  const totalPendingFiles = imageVideoFileList.length + audioFileList.length

  return (
    <div>
      <h2>Media Upload</h2>

      {/* Image & Video Section */}
      <div className="material-section">
        <h3 className="section-title">Image & Video ({imageVideoFileList.length} pending)</h3>
        <Dragger {...imageVideoUploadProps}>
          <p className="ant-upload-drag-icon">
            <VideoIcon />
          </p>
          <p className="ant-upload-text">Click this area to select images/videos</p>
          <p className="ant-upload-hint">
            (.jpeg, .jpg, .png, .mp4, .mov)
            <br />
            Files will be stored locally on this server
          </p>
        </Dragger>

        {/* Pending Files */}
        <div className="file-list-section">
          <PendingFileGroup
            title="Images"
            files={pendingImageFiles}
            onRemove={handleImageVideoFileRemove}
          />
          <PendingFileGroup
            title="Videos"
            files={pendingVideoFiles}
            onRemove={handleImageVideoFileRemove}
          />
        </div>
      </div>

      {/* Music Section */}
      <div className="material-section">
        <h3 className="section-title">Music ({audioFileList.length} pending)</h3>
        <Dragger {...audioUploadProps}>
          <p className="ant-upload-drag-icon">
            <MusicIcon />
          </p>
          <p className="ant-upload-text">Click this area to select music files</p>
          <p className="ant-upload-hint">
            (.mp3, .m4a, .wav) - Multiple files allowed
            <br />
            Files will be stored locally on this server
          </p>
        </Dragger>

        {/* Pending Music Files */}
        <div className="file-list-section">
          <PendingFileGroup
            title="Music"
            files={audioFileList}
            onRemove={handleAudioFileRemove}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <Button
          onClick={handleClearPending}
          disabled={totalPendingFiles === 0}
          className="clear-button"
        >
          Clear Pending ({totalPendingFiles})
        </Button>
        <Button
          type="primary"
          onClick={handleUpload}
          disabled={totalPendingFiles === 0}
          loading={uploading}
          className="upload-button"
        >
          {uploading ? 'Uploading...' : `Upload ${totalPendingFiles > 0 ? `(${totalPendingFiles})` : ''}`}
        </Button>
      </div>
    </div>
  )
}

export default Material 