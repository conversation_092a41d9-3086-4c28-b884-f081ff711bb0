/* Material Sections */
.material-section {
  margin-bottom: 32px;
}

.material-section:last-child {
  margin-bottom: 0;
}

.section-title {
  color: #262626;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  padding-left: 4px;
}

/* File List Section */
.file-list-section {
  margin-top: 16px;
}

/* File Group Styles */
.file-group {
  margin-bottom: 12px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  overflow: hidden;
  background-color: #ffffff;
}

.file-group:last-child {
  margin-bottom: 0;
}

.file-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.file-group-header:hover {
  background-color: #f0f0f0;
}

.file-group-title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-group-arrow {
  font-size: 12px;
  color: #8c8c8c;
  transition: transform 0.2s ease;
}

.file-group-arrow.expanded {
  transform: rotate(90deg);
}

.file-group-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.file-group-summary {
  font-size: 13px;
  color: #8c8c8c;
}

.file-count {
  font-weight: 500;
}

.file-group-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.file-group-content.expanded {
  max-height: 400px;
  overflow-y: auto;
}

/* File Item Styles */
.file-item-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.file-item-modern:hover {
  background-color: #f9f9f9;
}

.file-item-modern:last-child {
  border-bottom: none;
}

.file-item-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  overflow: hidden;
}

.file-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.file-details {
  flex: 1;
  overflow: hidden;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #8c8c8c;
}

.file-type {
  color: #8c8c8c;
  text-transform: uppercase;
  font-weight: 500;
}

.separator {
  color: #d9d9d9;
}

.file-size {
  color: #8c8c8c;
  font-weight: 500;
}

.file-status {
  color: #fa8c16;
  font-weight: 500;
}

/* File Actions */
.file-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.delete-button {
  flex-shrink: 0;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.delete-button:hover {
  opacity: 1;
}

/* Action Buttons */
.action-buttons {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
}

.clear-button {
  flex: 1;
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
}

.upload-button {
  flex: 1;
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
}

.upload-button:disabled {
  opacity: 0.6;
} 