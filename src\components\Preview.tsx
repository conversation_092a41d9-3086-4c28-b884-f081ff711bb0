import React, { useState, useEffect } from 'react'
import { Segmented, But<PERSON>, App, Drawer } from 'antd'
import { SoundFilled, PlayCircleFilled, DeleteOutlined, PlusOutlined } from '@ant-design/icons'

const { useApp } = App
import { realDataStorage, type PreviewSlot, type MaterialFile, type MediaComposition } from '../data/storage'
import '../styles/preview.css'

type Mode = 'music' | 'media'

const Preview: React.FC = () => {
  const { message, modal } = useApp()
  const [currentMode, setCurrentMode] = useState<Mode>('music')
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [drawerType, setDrawerType] = useState<'music' | 'media'>('music')
  const [selectedSlotId, setSelectedSlotId] = useState<number | null>(null)
  
  // Audio player state for drawer (selection modal)
  const [drawerAudioPlaying, setDrawerAudioPlaying] = useState<string | null>(null)
  const [drawerAudioElements, setDrawerAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map())

  // State for managing slots data from storage
  const [musicSlots, setMusicSlots] = useState<PreviewSlot[]>([])
  const [mediaSlots, setMediaSlots] = useState<PreviewSlot[]>([])

  // Available content for selection
  const [availableMusicFiles, setAvailableMusicFiles] = useState<MaterialFile[]>([])
  const [availableMediaCompositions, setAvailableMediaCompositions] = useState<MediaComposition[]>([])

  // Load data on component mount
  useEffect(() => {
    loadPreviewData()
    loadAvailableContent()
  }, [])

  const loadPreviewData = () => {
    const musicSlotsData = realDataStorage.getPreviewSlots('music')
    const mediaSlotsData = realDataStorage.getPreviewSlots('media')
    setMusicSlots(musicSlotsData)
    setMediaSlots(mediaSlotsData)
  }

  const loadAvailableContent = () => {
    const musicFiles = realDataStorage.getMaterialFiles('music')
    const mediaCompositions = realDataStorage.getMediaCompositions()
    setAvailableMusicFiles(musicFiles)
    setAvailableMediaCompositions(mediaCompositions)
  }

  // Load specific type of content on demand when opening drawer
  const loadAvailableContentByType = (type: 'music' | 'media') => {
    if (type === 'music') {
      const musicFiles = realDataStorage.getMaterialFiles('music')
      setAvailableMusicFiles(musicFiles)
    } else {
      const mediaCompositions = realDataStorage.getMediaCompositions()
      setAvailableMediaCompositions(mediaCompositions)
    }
  }

  const currentSlots = currentMode === 'music' ? musicSlots : mediaSlots

  const handleRemoveSlot = async (slotId: number) => {
    const slot = currentSlots.find(s => s.id === slotId)
    if (!slot || slot.isEmpty) return

    modal.confirm({
      title: 'Remove Content',
      content: `Are you sure you want to remove content from "${slot.title}"? This action cannot be undone.`,
      okText: 'Remove',
      centered: true,
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const success = await realDataStorage.clearPreviewSlot(slotId)
          if (success) {
            loadPreviewData() // Refresh slot data
            message.success(`Content removed from ${slot.title} successfully.`)
          } else {
            message.error(`Failed to remove content from ${slot.title}.`)
          }
        } catch (error) {
          console.error('Remove slot error:', error)
          message.error(`Failed to remove content from ${slot.title}.`)
        }
      },
    })
  }

  const handleAddContent = (slotId: number) => {
    console.log(`Add content to slot ${slotId} in ${currentMode} mode`)
    setSelectedSlotId(slotId)
    setDrawerType(currentMode === 'music' ? 'music' : 'media')
    // Load latest content of the specific type when opening drawer
    loadAvailableContentByType(currentMode === 'music' ? 'music' : 'media')
    setDrawerOpen(true)
  }

  const getDrawerTitle = () => {
    return drawerType === 'music' ? 'Select Background Music' : 'Select Media Content'
  }

  // Handle permanent deletion from storage (for drawer cards)
  const handlePermanentDelete = async (file: MaterialFile | MediaComposition) => {
    const fileName = file.name
    const isComposition = 'components' in file
    
    modal.confirm({
      title: 'Delete File Permanently',
      content: `Are you sure you want to permanently delete "${fileName}"? This action cannot be undone and will remove the file from storage.`,
      okText: 'Delete Permanently',
      centered: true,
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          let success = false
          
          if (isComposition) {
            // Delete media composition
            success = await realDataStorage.deleteMediaComposition(file.id)
          } else {
            // Delete material file
            success = await realDataStorage.deleteMaterialFile(file.id)
          }
          
          if (success) {
            // Refresh available content
            loadAvailableContent()
            message.success(`${fileName} deleted permanently.`)
          } else {
            message.error(`Failed to delete ${fileName}.`)
          }
        } catch (error) {
          console.error('Delete error:', error)
          message.error(`Failed to delete ${fileName}.`)
        }
      },
    })
  }

  // Handle drawer music play for real audio playback
  const handleDrawerMusicPlay = (file: MaterialFile) => {
    const isPlaying = drawerAudioPlaying === file.id
    
    if (isPlaying) {
      // Stop current playing audio
      const audioElement = drawerAudioElements.get(file.id)
      if (audioElement) {
        audioElement.pause()
        audioElement.currentTime = 0
      }
      setDrawerAudioPlaying(null)
    } else {
      // Stop any currently playing audio
      drawerAudioElements.forEach((audio, id) => {
        if (id !== file.id) {
          audio.pause()
          audio.currentTime = 0
        }
      })
      
      // Start new audio
      let audioElement = drawerAudioElements.get(file.id)
      if (!audioElement) {
        audioElement = new Audio(file.url)
        audioElement.addEventListener('ended', () => {
          setDrawerAudioPlaying(null)
        })
        setDrawerAudioElements(prev => new Map(prev.set(file.id, audioElement!)))
      }
      
      audioElement.play()
      setDrawerAudioPlaying(file.id)
    }
  }

  const handleFileSelect = async (file: MaterialFile | MediaComposition) => {
    if (!selectedSlotId) return

    try {
      let content: PreviewSlot['content']

      if (drawerType === 'music' && 'mimeType' in file) {
        // Material music file
        const musicFile = file as MaterialFile
        content = {
          type: 'material_music',
          fileId: musicFile.id,
          preview: {
            image: 'https://placehold.co/600x400/4f46e5/ffffff?text=Music',
            title: musicFile.name
          }
        }
      } else if (drawerType === 'media' && 'components' in file) {
        // Media composition
        const mediaComposition = file as MediaComposition
        content = {
          type: 'media_composition',
          fileId: mediaComposition.id,
          preview: {
            image: mediaComposition.preview.thumbnail,
            title: mediaComposition.name
          }
        }
      } else {
        message.error('Invalid file type for this slot.')
        return
      }

      const success = await realDataStorage.updatePreviewSlot(selectedSlotId, content)
      if (success) {
        loadPreviewData() // Refresh slot data
        message.success(`Content added to slot successfully.`)
        setDrawerOpen(false)
        setSelectedSlotId(null)
      } else {
        message.error('Failed to update slot.')
      }
    } catch (error) {
      console.error('File select error:', error)
      message.error('Failed to add content to slot.')
    }
  }

  const renderMusicContent = () => (
    <div className="drawer-file-grid single-col">
      {availableMusicFiles.length === 0 ? (
        <div className="empty-state">
          <p>No music files available.</p>
          <p>Please upload music files in the Material section first.</p>
        </div>
      ) : (
        availableMusicFiles.map((file) => (
          <div key={file.id} className="music-card" onClick={() => handleFileSelect(file)}>
            <div className="music-card-header">
              <h3 className="music-card-title">{file.name}</h3>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  handlePermanentDelete(file)
                }}
                size="small"
                className="file-card-delete-btn"
                title="Delete Permanently"
              />
            </div>
            <div className="music-player">
              <button
                className="music-play-btn"
                onClick={(e) => {
                  e.stopPropagation()
                  handleDrawerMusicPlay(file)
                }}
                title={drawerAudioPlaying === file.id ? "Pause" : "Play"}
              >
                {drawerAudioPlaying === file.id ? (
                  <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path fillRule="evenodd" d="M6.75 5.25a.75.75 0 01.75-.75H9a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H7.5a.75.75 0 01-.75-.75V5.25zm7.5 0A.75.75 0 0115 4.5h1.5a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H15a.75.75 0 01-.75-.75V5.25z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path fillRule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
              <div className="music-info">
                <div className="music-duration">{file.duration || '0:00'}</div>
                <div className="music-progress">
                  <div className="progress-bar">
                    <div className="progress-fill" style={{ width: '0%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  )

  const renderMediaContent = () => (
    <div>
      {availableMediaCompositions.length === 0 ? (
        <div className="empty-state">
          <p>No media compositions available.</p>
          <p>Please create media compositions in the Media section first.</p>
        </div>
      ) : (
        <div className="drawer-file-grid two-col">
          {availableMediaCompositions.map((composition) => (
            <div key={composition.id} className="file-card" onClick={() => handleFileSelect(composition)}>
              <div className="file-card-header">
                <h3 className="file-card-title">{composition.name}</h3>
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.stopPropagation()
                    handlePermanentDelete(composition)
                  }}
                  size="small"
                  className="file-card-delete-btn"
                  title="Delete Permanently"
                />
              </div>
              <div className="file-card-preview">
                <img
                  src={composition.preview.thumbnail}
                  alt={composition.name}
                  className="file-preview-image"
                />
              </div>
              <div className="file-card-meta">
                <span className="composition-type">{composition.type.replace('_', ' ').toUpperCase()}</span>
                {composition.preview.duration && (
                  <span className="composition-duration">{composition.preview.duration}</span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )

  const renderDrawerContent = () => {
    return drawerType === 'music' ? renderMusicContent() : renderMediaContent()
  }

  const modeOptions = [
    {
      label: 'Music Mode',
      value: 'music',
      icon: <SoundFilled />,
    },
    {
      label: 'Media Mode',
      value: 'media',
      icon: <PlayCircleFilled />,
    },
  ]

  return (
    <App>
      <div className="preview-container">
        {/* Antd Segmented Component */}
        <div className="mode-switcher-wrapper">
          <Segmented
            options={modeOptions}
            value={currentMode}
            block
            onChange={(value) => setCurrentMode(value as Mode)}
            size="large"
          />
        </div>

        {/* Cards Grid */}
        <div className={`drawer-file-grid ${currentMode === 'music' ? 'two-col' : 'single-col'}`}>
          {currentSlots.map((slot) => (
            slot.isEmpty ? (
              <div key={slot.id} className="file-card empty-slot" onClick={() => handleAddContent(slot.id)}>
                <div className="file-card-header">
                  <h3 className="file-card-title">{slot.title}</h3>
                </div>
                <div className="file-card-preview empty-preview">
                  <div className="placeholder-icon-wrapper-preview">
                    <PlusOutlined style={{ fontSize: '32px', color: '#9ca3af' }} />
                  </div>
                  <span className="placeholder-text">
                    {currentMode === 'music' ? 'Add Music' : 'Add Media'}
                  </span>
                </div>
              </div>
            ) : (
              <div key={slot.id} className="file-card">
                <div className="file-card-header">
                  <h3 className="file-card-title">{slot.title}</h3>
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    className="file-remove-btn"
                    onClick={() => handleRemoveSlot(slot.id)}
                  />
                </div>
                <div className="file-card-preview">
                  <img
                    src={slot.content?.preview.image || ''}
                    alt={slot.content?.preview.title || slot.title}
                    className="file-preview-image"
                  />
                </div>
                {slot.content && (
                  <div className="file-card-meta">
                    <span className="content-title">{slot.content.preview.title}</span>
                    <span className="content-type">{slot.content.type.replace('_', ' ').toUpperCase()}</span>
                  </div>
                )}
              </div>
            )
          ))}
        </div>

        {/* Bottom Drawer for File Selection */}
        <Drawer
          title={getDrawerTitle()}
          placement="bottom"
          open={drawerOpen}
          onClose={() => {
            // Stop any playing drawer audio when closing
            drawerAudioElements.forEach((audio) => {
              audio.pause()
              audio.currentTime = 0
            })
            setDrawerAudioPlaying(null)
            setDrawerOpen(false)
            setSelectedSlotId(null)
          }}
          height="80%"
        >
          {renderDrawerContent()}
        </Drawer>
      </div>
    </App>
  )
}

export default Preview