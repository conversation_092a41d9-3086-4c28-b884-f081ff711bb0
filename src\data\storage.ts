// Real file storage system for local server project

// File storage directory structure
export const STORAGE_CONFIG = {
  baseDir: 'led-fans-storage',
  directories: {
    materials: {
      images: 'files/materials/images',
      videos: 'files/materials/videos', 
      music: 'files/materials/music'
    },
    compositions: 'files/media-compositions',
    metadata: 'metadata'
  },
  metadata: {
    files: 'metadata/files.json',
    compositions: 'metadata/compositions.json',
    previewSlots: 'metadata/preview-slots.json'
  }
}

// Base file interface
export interface BaseFile {
  id: string
  name: string
  type: 'image' | 'video' | 'music'
  size: number
  localPath: string // Local file system path
  url: string // Local server URL for access
  duration?: string // Only for audio/video
  uploadTime: string
  mimeType: string
}

// Original files uploaded in Material section
export interface MaterialFile extends BaseFile {
  source: 'material'
  status: 'uploaded' | 'processing' | 'ready'
  originalName: string // Keep original filename
}

// Combined files created in Media section
export interface MediaComposition {
  id: string
  name: string
  type: 'video_music' | 'image_slideshow_music'
  createTime: string
  status: 'created' | 'processing' | 'ready'
  outputPath: string // Generated composition file path
  
  // Component parts references
  components: {
    video?: string // Material file ID
    images?: Array<{
      fileId: string // Material file ID
      timestamp: string // Time point in format "mm:ss"
      order: number
    }>
    music?: string // Material file ID
  }
  
  // Generated preview
  preview: {
    thumbnail: string // Generated thumbnail path
    duration?: string
    url: string // Local server URL for playback
  }
}

// Preview slot data
export interface PreviewSlot {
  id: number
  title: string
  mode: 'music' | 'media'
  isEmpty: boolean
  
  // Content reference
  content?: {
    type: 'material_music' | 'media_composition'
    fileId: string // MaterialFile.id or MediaComposition.id
    preview: {
      image: string
      title: string
    }
  }
}

// File system operations interface
interface FileSystemAPI {
  createDirectory(path: string): Promise<void>
  saveFile(file: File, path: string): Promise<string>
  deleteFile(path: string): Promise<void>
  readMetadata<T>(fileName: string): Promise<T | null>
  saveMetadata<T>(fileName: string, data: T): Promise<void>
  getFileUrl(path: string): string
}

// File system implementation using modern APIs with fallback
class LocalFileSystem implements FileSystemAPI {
  private baseUrl: string
  
  constructor() {
    // Local server base URL for file access
    this.baseUrl = window.location.origin
  }

  async createDirectory(path: string): Promise<void> {
    // In a real implementation, this would create directories
    // For now, we'll use a simple approach with URL-based paths
    console.log(`Creating directory: ${path}`)
  }

  async saveFile(file: File): Promise<string> {
    try {
      // Create FormData for file upload
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Upload failed: ${response.statusText} - ${errorText}`)
      }
      
      const result = await response.json()
      console.log('Upload response:', result)
      
      // Return the URL from server response (this is what the frontend should use)
      return result.url
      
    } catch (error) {
      console.error('File save error:', error)
      // Fallback: create object URL for immediate use
      const objectUrl = URL.createObjectURL(file)
      return objectUrl
    }
  }

  async deleteFile(path: string): Promise<void> {
    try {
      const response = await fetch('/api/delete', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ path })
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Delete failed: ${response.statusText} - ${errorText}`)
      }
      
      const result = await response.json()
      console.log('Delete response:', result)
    } catch (error) {
      console.error('File delete error:', error)
      throw error // Re-throw to allow handling in calling code
    }
  }

  async readMetadata<T>(fileName: string): Promise<T | null> {
    try {
      const response = await fetch(`/api/metadata/${fileName}`)
      if (response.ok) {
        return await response.json()
      }
      return null
    } catch (error) {
      console.error('Metadata read error:', error)
      // Fallback to localStorage for development
      const data = localStorage.getItem(`led-fans-${fileName}`)
      return data ? JSON.parse(data) : null
    }
  }

  async saveMetadata<T>(fileName: string, data: T): Promise<void> {
    try {
      await fetch('/api/metadata', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ fileName, data })
      })
    } catch (error) {
      console.error('Metadata save error:', error)
      // Fallback to localStorage for development
      localStorage.setItem(`led-fans-${fileName}`, JSON.stringify(data))
    }
  }

  getFileUrl(path: string): string {
    // Return local server URL for file access
    return `${this.baseUrl}/files/${path}`
  }
}

// Data storage manager
class RealDataStorage {
  private static instance: RealDataStorage
  private fileSystem: FileSystemAPI
  private materialFiles: MaterialFile[] = []
  private mediaCompositions: MediaComposition[] = []
  private previewSlots: PreviewSlot[] = []

  private constructor() {
    this.fileSystem = new LocalFileSystem()
    this.loadAllData()
  }

  static getInstance(): RealDataStorage {
    if (!RealDataStorage.instance) {
      RealDataStorage.instance = new RealDataStorage()
    }
    return RealDataStorage.instance
  }

  // Initialize storage directories
  private async initializeDirectories() {
    const dirs = [
      STORAGE_CONFIG.directories.materials.images,
      STORAGE_CONFIG.directories.materials.videos,
      STORAGE_CONFIG.directories.materials.music,
      STORAGE_CONFIG.directories.compositions,
      STORAGE_CONFIG.directories.metadata
    ]
    
    for (const dir of dirs) {
      await this.fileSystem.createDirectory(dir)
    }
  }

  // Load all data from metadata files
  private async loadAllData() {
    try {
      await this.initializeDirectories()
      
      const [materials, compositions, slots] = await Promise.all([
        this.fileSystem.readMetadata<MaterialFile[]>('files.json'),
        this.fileSystem.readMetadata<MediaComposition[]>('compositions.json'),
        this.fileSystem.readMetadata<PreviewSlot[]>('preview-slots.json')
      ])
      
      this.materialFiles = materials || []
      this.mediaCompositions = compositions || []
      this.previewSlots = slots || this.createDefaultPreviewSlots()
      
    } catch (error) {
      console.error('Failed to load data:', error)
      this.previewSlots = this.createDefaultPreviewSlots()
    }
  }

  // Save all metadata
  private async saveAllData() {
    try {
      await Promise.all([
        this.fileSystem.saveMetadata('files.json', this.materialFiles),
        this.fileSystem.saveMetadata('compositions.json', this.mediaCompositions),
        this.fileSystem.saveMetadata('preview-slots.json', this.previewSlots)
      ])
    } catch (error) {
      console.error('Failed to save data:', error)
    }
  }

  // Create default preview slots
  private createDefaultPreviewSlots(): PreviewSlot[] {
    return [
      // Music slots (1-8)
      ...Array.from({ length: 8 }, (_, i) => ({
        id: i + 1,
        title: `Music Slot ${i + 1}`,
        mode: 'music' as const,
        isEmpty: true
      })),
      // Media slots (11-16)
      ...Array.from({ length: 6 }, (_, i) => ({
        id: i + 11,
        title: `Media Slot ${i + 1}`,
        mode: 'media' as const,
        isEmpty: true
      }))
    ]
  }

  // Material file operations
  async addMaterialFile(file: File): Promise<MaterialFile> {
    try {
      // Determine file type
      let fileType: 'image' | 'video' | 'music'
      
      if (file.type.startsWith('image/')) {
        fileType = 'image'
      } else if (file.type.startsWith('video/')) {
        fileType = 'video'
      } else if (file.type.startsWith('audio/')) {
        fileType = 'music'
      } else {
        throw new Error(`Unsupported file type: ${file.type}`)
      }

      // Save file to local storage via backend
      const url = await this.fileSystem.saveFile(file, '')
      
      // Check if upload was successful (not a blob URL)
      if (url.startsWith('blob:')) {
        // Upload failed, using fallback
        throw new Error('File upload to server failed')
      }

      // Create material file record
      const materialFile: MaterialFile = {
        id: `material_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        originalName: file.name,
        type: fileType,
        size: file.size,
        localPath: url, // Store the URL as localPath for now
        url,
        uploadTime: new Date().toISOString(),
        mimeType: file.type,
        source: 'material',
        status: 'ready'
      }

      // Add duration for audio/video files
      if (fileType === 'music' || fileType === 'video') {
        materialFile.duration = await this.extractDuration(file)
      }

      this.materialFiles.push(materialFile)
      await this.saveAllData()
      
      return materialFile
      
    } catch (error) {
      console.error('Error adding material file:', error)
      throw error
    }
  }

  // Extract duration from audio/video files
  private async extractDuration(file: File): Promise<string> {
    return new Promise((resolve) => {
      const element = file.type.startsWith('video/') 
        ? document.createElement('video')
        : document.createElement('audio')
      
      element.src = URL.createObjectURL(file)
      element.onloadedmetadata = () => {
        const duration = element.duration
        const minutes = Math.floor(duration / 60)
        const seconds = Math.floor(duration % 60)
        resolve(`${minutes}:${seconds.toString().padStart(2, '0')}`)
        URL.revokeObjectURL(element.src)
      }
      element.onerror = () => {
        resolve('0:00')
        URL.revokeObjectURL(element.src)
      }
    })
  }

  getMaterialFiles(type?: 'image' | 'video' | 'music'): MaterialFile[] {
    return type 
      ? this.materialFiles.filter(f => f.type === type && f.status === 'ready')
      : this.materialFiles.filter(f => f.status === 'ready')
  }

  async deleteMaterialFile(id: string): Promise<boolean> {
    const fileIndex = this.materialFiles.findIndex(f => f.id === id)
    if (fileIndex === -1) return false

    const file = this.materialFiles[fileIndex]
    
    try {
      // Delete physical file
      await this.fileSystem.deleteFile(file.localPath)
      
      // Remove from metadata
      this.materialFiles.splice(fileIndex, 1)
      await this.saveAllData()
      
      return true
    } catch (error) {
      console.error('Error deleting material file:', error)
      return false
    }
  }

  // Media composition operations
  async addMediaComposition(composition: Omit<MediaComposition, 'id' | 'createTime' | 'outputPath' | 'preview'>): Promise<MediaComposition> {
    const id = `media_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Generate output path for composition
    const outputPath = `${STORAGE_CONFIG.directories.compositions}/${id}.mp4`
    
    const newComposition: MediaComposition = {
      ...composition,
      id,
      createTime: new Date().toISOString(),
      outputPath,
      preview: {
        thumbnail: `${outputPath}_thumb.jpg`,
        duration: composition.type === 'video_music' ? '0:00' : '0:00',
        url: this.fileSystem.getFileUrl(outputPath)
      }
    }

    this.mediaCompositions.push(newComposition)
    await this.saveAllData()
    
    return newComposition
  }

  getMediaCompositions(): MediaComposition[] {
    return this.mediaCompositions.filter(c => c.status === 'ready')
  }

  async deleteMediaComposition(id: string): Promise<boolean> {
    const compositionIndex = this.mediaCompositions.findIndex(c => c.id === id)
    if (compositionIndex === -1) return false

    const composition = this.mediaCompositions[compositionIndex]
    
    try {
      // Delete composition files
      await this.fileSystem.deleteFile(composition.outputPath)
      await this.fileSystem.deleteFile(composition.preview.thumbnail)
      
      // Remove from metadata
      this.mediaCompositions.splice(compositionIndex, 1)
      await this.saveAllData()
      
      return true
    } catch (error) {
      console.error('Error deleting media composition:', error)
      return false
    }
  }

  // Preview slot operations
  getPreviewSlots(mode: 'music' | 'media'): PreviewSlot[] {
    return this.previewSlots.filter(s => s.mode === mode)
  }

  async updatePreviewSlot(slotId: number, content: PreviewSlot['content']): Promise<boolean> {
    const slot = this.previewSlots.find(s => s.id === slotId)
    if (!slot) return false

    slot.content = content
    slot.isEmpty = !content
    await this.saveAllData()
    
    return true
  }

  async clearPreviewSlot(slotId: number): Promise<boolean> {
    const slot = this.previewSlots.find(s => s.id === slotId)
    if (!slot) return false

    slot.content = undefined
    slot.isEmpty = true
    await this.saveAllData()
    
    return true
  }

  // Get file details for Preview display
  getFileDetails(type: 'material_music' | 'media_composition', fileId: string): MaterialFile | MediaComposition | undefined {
    if (type === 'material_music') {
      return this.materialFiles.find(f => f.id === fileId)
    } else {
      return this.mediaCompositions.find(c => c.id === fileId)
    }
  }

  // Clear all data and files
  async clearAllData(): Promise<void> {
    try {
      // Delete all material files
      for (const file of this.materialFiles) {
        await this.fileSystem.deleteFile(file.localPath)
      }
      
      // Delete all composition files
      for (const composition of this.mediaCompositions) {
        await this.fileSystem.deleteFile(composition.outputPath)
        await this.fileSystem.deleteFile(composition.preview.thumbnail)
      }
      
      // Reset data
      this.materialFiles = []
      this.mediaCompositions = []
      this.previewSlots = this.createDefaultPreviewSlots()
      
      await this.saveAllData()
      
    } catch (error) {
      console.error('Error clearing all data:', error)
      throw error
    }
  }

  // Get storage statistics
  getStorageStats() {
    const materialFiles = this.getMaterialFiles()
    const mediaCompositions = this.getMediaCompositions()
    const musicSlots = this.getPreviewSlots('music')
    const mediaSlots = this.getPreviewSlots('media')

    return {
      materialFiles: {
        total: materialFiles.length,
        images: materialFiles.filter(f => f.type === 'image').length,
        videos: materialFiles.filter(f => f.type === 'video').length,
        music: materialFiles.filter(f => f.type === 'music').length,
        totalSize: materialFiles.reduce((sum, f) => sum + f.size, 0)
      },
      mediaCompositions: {
        total: mediaCompositions.length,
        videoMusic: mediaCompositions.filter(c => c.type === 'video_music').length,
        slideshow: mediaCompositions.filter(c => c.type === 'image_slideshow_music').length,
      },
      previewSlots: {
        musicFilled: musicSlots.filter(s => !s.isEmpty).length,
        musicEmpty: musicSlots.filter(s => s.isEmpty).length,
        mediaFilled: mediaSlots.filter(s => !s.isEmpty).length,
        mediaEmpty: mediaSlots.filter(s => s.isEmpty).length,
      }
    }
  }
}

// Export singleton instance
export const realDataStorage = RealDataStorage.getInstance()

// Utility functions
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2))
  return `${size} ${sizes[i]}`
}

export const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
} 