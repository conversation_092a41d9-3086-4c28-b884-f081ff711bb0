/* Mode Switcher - Antd Segmented */
.mode-switcher-wrapper {
  margin-bottom: 24px;
}

/* Use Media.tsx drawer grid styles */
.drawer-file-grid {
  display: grid;
  gap: 16px;
  width: 100%;
}

/* Single column layout for media mode */
.drawer-file-grid.single-col {
  grid-template-columns: 1fr;
  max-width: 400px;
  margin: 0 auto;
}

/* Two column layout for music mode */
.drawer-file-grid.two-col {
  grid-template-columns: 1fr 1fr;
  max-width: 800px;
  margin: 0 auto;
}

/* File Card Styles - From Media.tsx */
.file-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 12px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.file-card-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.file-remove-btn {
  color: #999;
  opacity: 0.6;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.file-card-preview {
  width: 100%;
  height: 200px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.file-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Empty slot styles - More empty-like, less disabled-like */
.empty-slot {
  border: 2px dashed #e5e7eb;
  background: #ffffff;
  transition: all 0.2s ease;
}

.empty-slot:hover {
  border-color: #9ca3af;
  background: #f9fafb;
}

.empty-preview {
  flex-direction: column;
  gap: 12px;
  padding: 20px;
}

.placeholder-icon-wrapper-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  background: #f3f4f6;
}

.placeholder-text {
  font-size: 14px;
  color: #9ca3af;
  font-weight: 500;
  text-align: center;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .drawer-file-grid.two-col {
    gap: 12px;
  }
  
  .file-card-header {
    padding: 12px 12px 8px 12px;
  }
  
  .file-card-title {
    font-size: 13px;
  }
  
  .file-card-preview {
    height: 160px;
  }
  
  .placeholder-text {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .drawer-file-grid.two-col {
    gap: 8px;
  }
  
  .file-card-header {
    padding: 10px 10px 6px 10px;
  }
  
  .file-card-title {
    font-size: 12px;
  }
  
  .file-card-preview {
    height: 140px;
  }
}

/* Music Card Styles - From Media.tsx */
.music-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.music-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 12px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.music-card-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.music-player {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
  background: #f8f9fa;
}

.music-play-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #1677ff;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.music-play-btn .play-icon {
  width: 20px;
  height: 20px;
}

.music-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.music-duration {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.music-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1677ff;
  border-radius: 2px;
  transition: width 0.3s ease;
} 