/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  line-height: 1.6;
}

#root {
  min-height: 100vh;
  width: 100%;
  padding: 0;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

@media (min-width: 769px) {
  body {
    font-size: 16px;
  }
}
