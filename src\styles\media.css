/* Primary Media Section */
.primary-media-section {
  margin-bottom: 24px;
}

/* Segmented Control */
.media-segmented {
  margin-bottom: 24px;
}

/* Music Section */
.music-section {
  margin-top: 24px;
}

.music-divider-text {
  color: #8c8c8c;
  font-weight: 500;
  font-size: 14px;
  padding: 0 16px;
}

/* Media Sections */
.media-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
}

/* Upload Placeholder Buttons */
.upload-placeholder {
  position: relative;
  display: block;
  width: 100%;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
  padding: 48px 12px;
  text-align: center;
  background: #ffffff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.placeholder-icon-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.placeholder-icon {
  margin: 0 auto 8px auto;
  width: 48px;
  height: 48px;
  color: #9ca3af;
}

.placeholder-text {
  display: block;
  margin-top: 8px;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.placeholder-hint {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #6b7280;
}

/* File List Section for Selected Files */
.file-list-section {
  margin-top: 16px;
}

.selected-file-list {
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  overflow: hidden;
  background-color: #ffffff;
}

/* File Item Styles (from Material.tsx) */
.file-item-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.file-item-modern:last-child {
  border-bottom: none;
}

.file-item-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  overflow: hidden;
}

.file-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.file-details {
  flex: 1;
  overflow: hidden;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #8c8c8c;
}

.file-type {
  color: #8c8c8c;
  text-transform: uppercase;
  font-weight: 500;
}

.separator {
  color: #d9d9d9;
}

.file-size {
  color: #8c8c8c;
  font-weight: 500;
}

.file-duration {
  color: #8c8c8c;
  font-weight: 500;
}

/* File Actions */
.file-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.preview-button {
  opacity: 0.6;
  transition: opacity 0.2s ease;
  color: #1677ff;
}

.delete-button {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

/* Lightweight Slideshow Header */
.slideshow-header {
  margin-bottom: 8px;
  padding: 6px 8px;
  background: #f9fafb;
  border-radius: 4px;
  font-size: 12px;
  color: #6b7280;
}

.slideshow-title {
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.time-series-list {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 4px;
}

/* Lightweight slide item */
.slide-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.slide-item:last-child {
  border-bottom: none;
}

.time-input {
  flex-shrink: 0;
  width: 80px;
}

.time-input .ant-picker {
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 11px;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
  min-width: 0;
}

.file-name {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.controls {
  display: flex;
  gap: 2px;
  flex-shrink: 0;
}

.controls .ant-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Preview Modal Styles */
.preview-modal .preview-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-modal .preview-info {
  padding: 16px 24px;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.preview-modal .preview-info p {
  margin: 0;
  margin-bottom: 8px;
  font-size: 14px;
  color: #595959;
}

.preview-modal .preview-info p:last-child {
  margin-bottom: 0;
}

.preview-modal .preview-info strong {
  color: #262626;
  font-weight: 500;
}

/* Music Player Preview in Modal */
.music-player-preview {
  display: flex;
  flex-direction: column;
  padding: 32px 24px 24px;
  max-width: 500px;
  margin: 0 auto;
  gap: 24px;
}

/* Music Info Header */
.music-info-header {
  text-align: center;
}

.music-title {
  font-size: 22px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 6px;
  line-height: 1.3;
}

/* Progress Section - Full Width */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.progress-track {
  position: relative;
  width: 100%;
  height: 8px;
  background: #f8fafc;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #f97316;
  border-radius: 4px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 16px;
  background: #f97316;
  border-radius: 2px;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9);
  transition: left 0.1s ease;
  cursor: pointer;
}

.time-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-time,
.total-time {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
  font-variant-numeric: tabular-nums;
}

.current-time {
  color: #1e293b;
}

/* Control Panel */
.music-control-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #f1f5f9;
}

.control-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 8px;
  border-radius: 50%;
}

.control-icon {
  width: 24px;
  height: 24px;
  color: #64748b;
}

/* Play button - matching music-play-btn style */
.play-btn {
  width: 56px;
  height: 56px;
  background: #334155;
  border-radius: 50%;
  padding: 0;
  margin: 0 8px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.play-icon {
  width: 24px;
  height: 24px;
  color: white;
}

/* Drawer File Grid Layout */
.drawer-file-grid {
  display: grid;
  gap: 16px;
  width: 100%;
}

/* Single column layout for video and music */
.drawer-file-grid.single-col {
  grid-template-columns: 1fr;
  max-width: 400px;
  margin: 0 auto;
}

/* Two column layout for images */
.drawer-file-grid.two-col {
  grid-template-columns: 1fr 1fr;
  max-width: 800px;
  margin: 0 auto;
}

/* File Card Styles */
.file-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
  cursor: pointer;
}

/* File Card Header */
.file-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 12px 16px;
  background: #fff;
}

.file-card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

/* File Remove Button */
.file-remove-btn {
  color: #999;
  border: none;
  background: none;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* File Card Delete Button (for permanent deletion) */
.file-card-delete-btn {
  opacity: 0.6;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.file-card-delete-btn:hover {
  opacity: 1;
}

.file-card:hover .file-card-delete-btn {
  opacity: 0.8;
}

/* File Card Preview */
.file-card-preview {
  width: 100%;
  aspect-ratio: 16/9;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Music Card Styles */
.music-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
  cursor: pointer;
}

.music-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 12px 16px;
  background: #fff;
}

.music-card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

/* Music Player */
.music-player {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

/* Music play button in drawer */
.music-play-btn {
  background: #334155;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.music-play-btn .play-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.play-btn {
  color: #1677ff;
  font-size: 32px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.music-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.music-duration {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

.music-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1677ff;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Action Buttons */
.action-buttons {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
}

.clear-button {
  flex: 1;
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
}

.create-button {
  flex: 1;
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
}

.create-button:disabled {
  opacity: 0.6;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  /* Music Player Preview - Mobile Optimizations */
  .music-player-preview {
    padding: 20px 16px 16px;
    gap: 20px;
    max-width: none;
    margin: 0;
  }

  .music-title {
    font-size: 18px;
    line-height: 1.2;
  }

  /* Control Panel - Mobile Layout */
  .music-control-panel {
    gap: 16px;
    padding: 12px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .play-btn {
    width: 48px;
    height: 48px;
    margin: 0 4px;
  }

  .play-icon {
    width: 20px;
    height: 20px;
  }

  .control-icon {
    width: 20px;
    height: 20px;
  }

  .control-btn {
    padding: 6px;
  }

  /* Progress Section - Mobile */
  .progress-section {
    gap: 10px;
  }

  .progress-track {
    height: 10px;
  }

  .progress-thumb {
    width: 6px;
    height: 20px;
  }

  .current-time,
  .total-time {
    font-size: 12px;
  }

  /* Preview Modal - Mobile */
  .preview-modal .ant-modal-content {
    margin: 8px;
  }

  .preview-modal .ant-modal-body {
    padding: 16px;
  }

  /* Drawer File Grid - Mobile */
  .drawer-file-grid.two-col {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .drawer-file-grid.single-col {
    max-width: none;
  }

  /* Music Card - Mobile */
  .music-card-header {
    padding: 12px 12px 8px 12px;
  }

  .music-card-title {
    font-size: 14px;
  }

  .music-player {
    padding: 8px 12px 12px 12px;
  }

  .music-play-btn {
    width: 36px;
    height: 36px;
  }

  .music-info {
    margin-left: 8px;
  }

  .music-duration {
    font-size: 11px;
  }
} 