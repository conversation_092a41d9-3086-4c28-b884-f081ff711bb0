import React, { useState } from 'react'
import dayjs from 'dayjs'
import { SoundFilled, PlayCircleFilled, FileImageOutlined } from '@ant-design/icons'
import { Segmented, Divider, <PERSON>er, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, TimePicker } from 'antd'
import { DeleteOutlined, <PERSON>Outlined, <PERSON>U<PERSON>Outlined, ArrowDownOutlined } from '@ant-design/icons'

const { useApp } = App
import {
  VideoIcon,
  ImageIcon,
  MusicIcon,
  SmallVideoIcon,
  SmallImageIcon,
  SmallMusicIcon
} from '../assets/icons'
import { realDataStorage, type MaterialFile, formatFileSize } from '../data/storage'
import '../styles/media.css'

// Import utility functions from Material.tsx
const getFileIcon = (fileType: string) => {
  const type = fileType?.toLowerCase() || ''

  // Check by file type string since we're using simplified type names
  if (type === 'image' || type.startsWith('image/')) {
    return <SmallImageIcon color="#52c41a" />
  }
  if (type === 'video' || type.startsWith('video/')) {
    return <SmallVideoIcon color="#c27aff" />
  }
  if (type === 'music' || type === 'audio' || type.startsWith('audio/')) {
    return <SmallMusicIcon color="#fa8c16" />
  }
  return <FileImageOutlined color="#ff2056" />
}

const truncateMiddle = (str: string, startLen = 20, endLen = 8) => {
  if (str.length <= startLen + endLen) return str
  return `${str.slice(0, startLen)}...${str.slice(-endLen)}`
}

// Selected file type - based on MaterialFile but simplified
interface SelectedFile {
  id: string
  name: string
  url: string
  duration?: string
  type: 'image' | 'video' | 'music'
  size: number
}

// File item component for displaying selected files
const FileItem = React.memo(({ file, onDelete, onPreview }: {
  file: SelectedFile,
  onDelete: (file: SelectedFile) => void,
  onPreview?: (file: SelectedFile) => void
}) => {
  const isPreviewable = file.type === 'image' || file.type === 'video' || file.type === 'music'

  return (
    <div className="file-item-modern">
      <div
        className="file-item-info"
        onClick={() => isPreviewable && onPreview?.(file)}
        style={{ cursor: isPreviewable ? 'pointer' : 'default' }}
      >
        <span className="file-icon">{getFileIcon(file.type)}</span>
        <div className="file-details">
          <div className="file-name">{truncateMiddle(file.name, 30)}</div>
          <div className="file-meta">
            <span className="file-type">{file.type.toUpperCase()}</span>
            <span className="separator">•</span>
            <span className="file-size">{formatFileSize(file.size)}</span>
            {file.duration && (
              <>
                <span className="separator">•</span>
                <span className="file-duration">{file.duration}</span>
              </>
            )}
          </div>
        </div>
      </div>
      <div className="file-actions">
        {isPreviewable && (
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => onPreview?.(file)}
            size="small"
            className="preview-button"
            title="Preview"
          />
        )}
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => onDelete(file)}
          size="small"
          className="delete-button"
          title="Delete"
        />
      </div>
    </div>
  )
})

// Time series image slide item component
const ImageSlideItem = React.memo(({
  image,
  index,
  onDelete,
  onPreview,
  onTimestampChange,
  onMoveUp,
  onMoveDown,
  canMoveUp,
  canMoveDown
}: {
  image: SelectedFile & { timestamp: string },
  index: number,
  onDelete: (file: SelectedFile & { timestamp: string }, index: number) => void,
  onPreview?: (file: SelectedFile) => void,
  onTimestampChange: (index: number, timestamp: string) => void,
  onMoveUp: (index: number) => void,
  onMoveDown: (index: number) => void,
  canMoveUp: boolean,
  canMoveDown: boolean
}) => {
  return (
    <div className="slide-item">
      <div className="time-input">
        <TimePicker
          value={image.timestamp ? dayjs(image.timestamp, 'mm:ss') : null}
          onChange={(time) => onTimestampChange(index, time ? time.format('mm:ss') : '00:00')}
          placeholder="00:02"
          size="small"
          format="mm:ss"
          showNow={false}
          allowClear={false}
        />
      </div>

      <div className="file-info" onClick={() => onPreview?.(image)}>
        <span className="file-name">{truncateMiddle(image.name, 25)}</span>
      </div>

      <div className="controls">
        <Button type="text" icon={<ArrowUpOutlined />} onClick={() => onMoveUp(index)} size="small" disabled={!canMoveUp} />
        <Button type="text" icon={<ArrowDownOutlined />} onClick={() => onMoveDown(index)} size="small" disabled={!canMoveDown} />
        <Button type="text" icon={<DeleteOutlined />} onClick={() => onDelete(image, index)} size="small" danger />
      </div>
    </div>
  )
})

const Media: React.FC = () => {
  const { message, modal } = useApp()
  const [segmentedValue, setSegmentedValue] = useState<string>('Video')
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [drawerType, setDrawerType] = useState<'video' | 'image' | 'music'>('video')
  const [uploading, setUploading] = useState(false)

  // Preview modal state
  const [previewOpen, setPreviewOpen] = useState(false)
  const [previewFile, setPreviewFile] = useState<SelectedFile | null>(null)

  // Audio player state for preview modal
  const [audioPlaying, setAudioPlaying] = useState(false)
  const [audioCurrentTime, setAudioCurrentTime] = useState(0)
  const [audioDuration, setAudioDuration] = useState(0)
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null)

  // Audio player state for drawer (selection modal)
  const [drawerAudioPlaying, setDrawerAudioPlaying] = useState<string | null>(null)
  const [drawerAudioElements, setDrawerAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map())

  // Selected files state - video and music: single file, images: multiple files for slideshow
  const [selectedVideo, setSelectedVideo] = useState<SelectedFile | null>(null)
  const [selectedImages, setSelectedImages] = useState<(SelectedFile & { timestamp: string })[]>([])
  const [selectedMusic, setSelectedMusic] = useState<SelectedFile | null>(null)

  // Material files loaded from storage - loaded on demand
  const [materialVideoFiles, setMaterialVideoFiles] = useState<MaterialFile[]>([])
  const [materialImageFiles, setMaterialImageFiles] = useState<MaterialFile[]>([])
  const [materialMusicFiles, setMaterialMusicFiles] = useState<MaterialFile[]>([])

  // Load specific type of files on demand
  const loadMaterialFilesByType = (type: 'video' | 'image' | 'music') => {
    const allFiles = realDataStorage.getMaterialFiles()

    switch (type) {
      case 'video':
        setMaterialVideoFiles(allFiles.filter(f => f.type === 'video'))
        break
      case 'image':
        setMaterialImageFiles(allFiles.filter(f => f.type === 'image'))
        break
      case 'music':
        setMaterialMusicFiles(allFiles.filter(f => f.type === 'music'))
        break
    }
  }

  // Convert MaterialFile to SelectedFile
  const materialToSelected = (material: MaterialFile): SelectedFile => ({
    id: material.id,
    name: material.name,
    url: material.url,
    duration: material.duration,
    type: material.type,
    size: material.size
  })

  const handlePlaceholderClick = (type: 'video' | 'image' | 'music') => {
    setDrawerType(type)
    // Load latest files of the specific type when opening drawer
    loadMaterialFilesByType(type)
    setDrawerOpen(true)
  }

  const getDrawerTitle = () => {
    switch (drawerType) {
      case 'video':
        return 'Select Video'
      case 'image':
        return 'Select Images'
      case 'music':
        return 'Select Background Music'
      default:
        return 'Select Media'
    }
  }

  // Handle drawer music play for real audio playback
  const handleDrawerMusicPlay = (file: MaterialFile) => {
    const isPlaying = drawerAudioPlaying === file.id

    if (isPlaying) {
      // Stop current playing audio
      const audioElement = drawerAudioElements.get(file.id)
      if (audioElement) {
        audioElement.pause()
        audioElement.currentTime = 0
      }
      setDrawerAudioPlaying(null)
    } else {
      // Stop any currently playing audio
      drawerAudioElements.forEach((audio, id) => {
        if (id !== file.id) {
          audio.pause()
          audio.currentTime = 0
        }
      })

      // Start new audio
      let audioElement = drawerAudioElements.get(file.id)
      if (!audioElement) {
        audioElement = new Audio(file.url)
        audioElement.addEventListener('ended', () => {
          setDrawerAudioPlaying(null)
        })
        setDrawerAudioElements(prev => new Map(prev.set(file.id, audioElement!)))
      }

      audioElement.play()
      setDrawerAudioPlaying(file.id)
    }
  }

  // Generate smart default timestamp for new image
  const generateDefaultTimestamp = (): string => {
    if (selectedImages.length === 0) {
      return '00:00'
    }

    // Get the last image's timestamp and add 2 seconds
    const lastTimestamp = selectedImages[selectedImages.length - 1].timestamp
    const [minutes, seconds] = lastTimestamp.split(':').map(Number)
    const totalSeconds = minutes * 60 + seconds + 2
    const newMinutes = Math.floor(totalSeconds / 60)
    const newSeconds = totalSeconds % 60

    return `${newMinutes.toString().padStart(2, '0')}:${newSeconds.toString().padStart(2, '0')}`
  }

  const handleFileSelect = (materialFile: MaterialFile) => {
    const selectedFile = materialToSelected(materialFile)

    // Add to appropriate section based on type
    switch (drawerType) {
      case 'video':
        setSelectedVideo(selectedFile)
        break
      case 'image': {
        // Add image to slideshow with smart default timestamp
        const defaultTimestamp = generateDefaultTimestamp()
        const newImageWithTimestamp = { ...selectedFile, timestamp: defaultTimestamp }
        setSelectedImages(prev => [...prev, newImageWithTimestamp])
        break
      }
      case 'music':
        setSelectedMusic(selectedFile)
        break
    }

    console.log('Selected file:', selectedFile)
    setDrawerOpen(false)
  }

  // Handle permanent deletion from storage (for drawer cards)
  const handlePermanentDelete = async (materialFile: MaterialFile) => {
    modal.confirm({
      title: 'Delete File Permanently',
      content: `Are you sure you want to permanently delete "${materialFile.name}"? This action cannot be undone and will remove the file from storage.`,
      okText: 'Delete Permanently',
      centered: true,
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const success = await realDataStorage.deleteMaterialFile(materialFile.id)
          if (success) {
            // Refresh the current file type list
            loadMaterialFilesByType(materialFile.type)
            message.success(`${materialFile.name} deleted permanently.`)
          } else {
            message.error(`Failed to delete ${materialFile.name}.`)
          }
        } catch (error) {
          console.error('Delete error:', error)
          message.error(`Failed to delete ${materialFile.name}.`)
        }
      },
    })
  }

  // Handle timestamp change for images
  const handleTimestampChange = (index: number, timestamp: string) => {
    setSelectedImages(prev => prev.map((img, i) =>
      i === index ? { ...img, timestamp } : img
    ))
  }

  // Handle moving image up in the list
  const handleMoveImageUp = (index: number) => {
    if (index === 0) return
    setSelectedImages(prev => {
      const newImages = [...prev]
      const temp = newImages[index]
      newImages[index] = newImages[index - 1]
      newImages[index - 1] = temp
      return newImages
    })
  }

  // Handle moving image down in the list
  const handleMoveImageDown = (index: number) => {
    if (index === selectedImages.length - 1) return
    setSelectedImages(prev => {
      const newImages = [...prev]
      const temp = newImages[index]
      newImages[index] = newImages[index + 1]
      newImages[index + 1] = temp
      return newImages
    })
  }

  const handleSelectedFileDelete = (file: SelectedFile | (SelectedFile & { timestamp: string }), index?: number) => {
    // Simple removal from selected list - no confirmation needed
    switch (file.type) {
      case 'video':
        setSelectedVideo(null)
        break
      case 'image':
        if (typeof index === 'number') {
          setSelectedImages(prev => prev.filter((_, i) => i !== index))
        }
        break
      case 'music':
        setSelectedMusic(null)
        break
    }
    message.success(`${file.name} removed from selection.`)
  }

  const handlePreview = (file: SelectedFile) => {
    // Open preview modal for all file types (video, image, music)
    setPreviewFile(file)
    setPreviewOpen(true)

    // Initialize audio player for music files
    if (file.type === 'music') {
      initializeAudioPlayer(file.url)
    }
  }

  // Initialize audio player for music preview
  const initializeAudioPlayer = (audioUrl: string) => {
    if (audioElement) {
      audioElement.pause()
      audioElement.removeEventListener('loadedmetadata', handleAudioLoadedMetadata)
      audioElement.removeEventListener('timeupdate', handleAudioTimeUpdate)
      audioElement.removeEventListener('ended', handleAudioEnded)
    }

    const audio = new Audio(audioUrl)
    audio.addEventListener('loadedmetadata', handleAudioLoadedMetadata)
    audio.addEventListener('timeupdate', handleAudioTimeUpdate)
    audio.addEventListener('ended', handleAudioEnded)

    setAudioElement(audio)
    setAudioPlaying(false)
    setAudioCurrentTime(0)
    setAudioDuration(0)
  }

  // Audio event handlers
  const handleAudioLoadedMetadata = (event: Event) => {
    const audio = event.target as HTMLAudioElement
    setAudioDuration(audio.duration)
  }

  const handleAudioTimeUpdate = (event: Event) => {
    const audio = event.target as HTMLAudioElement
    setAudioCurrentTime(audio.currentTime)
  }

  const handleAudioEnded = () => {
    setAudioPlaying(false)
    setAudioCurrentTime(0)
  }

  // Audio control functions
  const toggleAudioPlayback = () => {
    if (!audioElement) return

    if (audioPlaying) {
      audioElement.pause()
      setAudioPlaying(false)
    } else {
      audioElement.play()
      setAudioPlaying(true)
    }
  }

  const seekAudio = (seconds: number) => {
    if (!audioElement) return
    audioElement.currentTime = Math.max(0, Math.min(audioElement.duration, audioElement.currentTime + seconds))
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Close preview modal and cleanup audio
  const handlePreviewClose = () => {
    if (audioElement) {
      audioElement.pause()
      audioElement.removeEventListener('loadedmetadata', handleAudioLoadedMetadata)
      audioElement.removeEventListener('timeupdate', handleAudioTimeUpdate)
      audioElement.removeEventListener('ended', handleAudioEnded)
      setAudioElement(null)
    }
    setPreviewOpen(false)
    setPreviewFile(null)
    setAudioPlaying(false)
    setAudioCurrentTime(0)
    setAudioDuration(0)
  }

  // Get preview URL for modal
  const getPreviewUrl = (file: SelectedFile): string => {
    return file.url
  }

  const handleCreate = async () => {
    const hasContent = selectedVideo || selectedImages.length > 0 || selectedMusic
    if (!hasContent) {
      message.warning('Please select files to create media.')
      return
    }

    setUploading(true)

    try {
      // Determine composition type
      let compositionType: 'video_music' | 'image_slideshow_music'
      let compositionName: string

      if (selectedVideo) {
        compositionType = 'video_music'
        compositionName = `Video Mix - ${selectedVideo.name}${selectedMusic ? ` + ${selectedMusic.name}` : ''}`
      } else {
        compositionType = 'image_slideshow_music'
        compositionName = `Slideshow (${selectedImages.length} images)${selectedMusic ? ` + ${selectedMusic.name}` : ''}`
      }

      // Create composition data
      const composition = {
        name: compositionName,
        type: compositionType,
        status: 'ready' as const,
        components: {
          ...(selectedVideo && { video: selectedVideo.id }),
          ...(selectedImages.length > 0 && {
            images: selectedImages.map((img, index) => ({
              fileId: img.id,
              timestamp: img.timestamp,
              order: index
            }))
          }),
          ...(selectedMusic && { music: selectedMusic.id })
        }
      }

      // Save to storage
      await realDataStorage.addMediaComposition(composition)

      // Clear selections
      setSelectedVideo(null)
      setSelectedImages([])
      setSelectedMusic(null)

      message.success('Media composition created successfully.')

    } catch (error) {
      console.error('Create error:', error)
      message.error('Failed to create media composition.')
    } finally {
      setUploading(false)
    }
  }

  const handleClear = () => {
    const selectedFiles = [selectedVideo, selectedImages.length > 0 ? selectedImages : null, selectedMusic].filter(file => file !== null)
    if (selectedFiles.length === 0) {
      message.info('No files to clear.')
      return
    }

    modal.confirm({
      title: 'Clear All Files',
      content: `Are you sure you want to clear all selected file(s)? This action cannot be undone.`,
      okText: 'Clear',
      centered: true,
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        setSelectedVideo(null)
        setSelectedImages([])
        setSelectedMusic(null)
        message.success('All files cleared.')
      },
    })
  }

  const renderVideoContent = () => (
    <div className="drawer-file-grid single-col">
      {materialVideoFiles.length === 0 ? (
        <div className="empty-state">
          <p>No video files available.</p>
          <p>Please upload video files in the Material section first.</p>
        </div>
      ) : (
        materialVideoFiles.map((file) => (
          <div key={file.id} className="file-card" onClick={() => handleFileSelect(file)}>
            <div className="file-card-header">
              <h3 className="file-card-title">{file.name}</h3>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  handlePermanentDelete(file)
                }}
                size="small"
                className="file-card-delete-btn"
                title="Delete Permanently"
              />
            </div>
            <div className="file-card-preview">
              <img src={file.url} alt={file.name} className="file-preview-image" />
            </div>
          </div>
        ))
      )}
    </div>
  )

  const renderImageContent = () => (
    <div>
      {materialImageFiles.length === 0 ? (
        <div className="empty-state">
          <p>No image files available.</p>
          <p>Please upload image files in the Material section first.</p>
        </div>
      ) : (
        <>
          {materialImageFiles.length > 0 && (
            <div className="drawer-file-grid two-col">
              {materialImageFiles.map((file) => (
                <div key={file.id} className="file-card" onClick={() => handleFileSelect(file)}>
                  <div className="file-card-header">
                    <h3 className="file-card-title">{file.name}</h3>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handlePermanentDelete(file)
                      }}
                      size="small"
                      className="file-card-delete-btn"
                      title="Delete Permanently"
                    />
                  </div>
                  <div className="file-card-preview">
                    <img src={file.url} alt={file.name} className="file-preview-image" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  )

  const renderMusicContent = () => (
    <div className="drawer-file-grid single-col">
      {materialMusicFiles.length === 0 ? (
        <div className="empty-state">
          <p>No music files available.</p>
          <p>Please upload music files in the Material section first.</p>
        </div>
      ) : (
        materialMusicFiles.map((file) => (
          <div key={file.id} className="music-card" onClick={() => handleFileSelect(file)}>
            <div className="music-card-header">
              <h3 className="music-card-title">{file.name}</h3>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  handlePermanentDelete(file)
                }}
                size="small"
                className="file-card-delete-btn"
                title="Delete Permanently"
              />
            </div>
            <div className="music-player">
              <button
                className="music-play-btn"
                onClick={(e) => {
                  e.stopPropagation()
                  handleDrawerMusicPlay(file)
                }}
                title={drawerAudioPlaying === file.id ? "Pause" : "Play"}
              >
                {drawerAudioPlaying === file.id ? (
                  <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path fillRule="evenodd" d="M6.75 5.25a.75.75 0 01.75-.75H9a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H7.5a.75.75 0 01-.75-.75V5.25zm7.5 0A.75.75 0 0115 4.5h1.5a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H15a.75.75 0 01-.75-.75V5.25z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path fillRule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
              <div className="music-info">
                <div className="music-duration">{file.duration || '0:00'}</div>
                <div className="music-progress">
                  <div className="progress-bar">
                    <div className="progress-fill" style={{ width: '0%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  )

  const renderDrawerContent = () => {
    switch (drawerType) {
      case 'video':
        return renderVideoContent()
      case 'image':
        return renderImageContent()
      case 'music':
        return renderMusicContent()
      default:
        return null
    }
  }

  return (
    <div>
      {/* Primary Media Section */}
      <div className="primary-media-section">
        {/* Segmented Control */}
        <div className="media-segmented">
          <Segmented
            value={segmentedValue}
            onChange={setSegmentedValue}
            size="large"
            block
            options={[
              {
                label: 'Video Mode',
                value: 'Video',
                icon: <PlayCircleFilled />
              },
              {
                label: 'Image Mode',
                value: 'Image',
                icon: <SoundFilled />
              },
            ]}
          />
        </div>

        {/* Video Section */}
        {segmentedValue === 'Video' && (
          <div className="media-section">
            <h3 className="section-title">Video Selection</h3>
            <button
              type="button"
              className="upload-placeholder"
              onClick={() => handlePlaceholderClick('video')}
            >
              <div className="placeholder-icon-wrapper">
                <VideoIcon />
              </div>
              <span className="placeholder-text">Click this area to select / replace Video</span>
            </button>

            {/* Selected Video File List */}
            {selectedVideo && (
              <div className="file-list-section">
                <div className="selected-file-list">
                  <FileItem
                    file={selectedVideo}
                    onDelete={handleSelectedFileDelete}
                    onPreview={handlePreview}
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Image Section */}
        {segmentedValue === 'Image' && (
          <div className="media-section">
            <h3 className="section-title">Image Selection</h3>
            <button
              type="button"
              className="upload-placeholder"
              onClick={() => handlePlaceholderClick('image')}
            >
              <div className="placeholder-icon-wrapper">
                <ImageIcon />
              </div>
              <span className="placeholder-text">Click this area to select / replace Images</span>
            </button>

            {/* Selected Images File List - Time Series Slideshow */}
            {selectedImages.length > 0 && (
              <div className="file-list-section">
                <div className="slideshow-header">
                  <div className="slideshow-title">Image Timeline ({selectedImages.length})</div>
                </div>
                <div className="selected-file-list time-series-list">
                  {selectedImages.map((image, index) => (
                    <ImageSlideItem
                      key={`${image.id}-${index}`}
                      image={image}
                      index={index}
                      onDelete={handleSelectedFileDelete}
                      onPreview={handlePreview}
                      onTimestampChange={handleTimestampChange}
                      onMoveUp={handleMoveImageUp}
                      onMoveDown={handleMoveImageDown}
                      canMoveUp={index > 0}
                      canMoveDown={index < selectedImages.length - 1}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Music Section - Separated */}
      <Divider plain>
        <span className="music-divider-text">Background Music (Optional)</span>
      </Divider>

      <div className="music-section">
        <button
          type="button"
          className="upload-placeholder"
          onClick={() => handlePlaceholderClick('music')}
        >
          <div className="placeholder-icon-wrapper">
            <MusicIcon />
          </div>
          <span className="placeholder-text">Click this area to select / replace Music</span>
        </button>

        {/* Selected Music File List */}
        {selectedMusic && (
          <div className="file-list-section">
            <div className="selected-file-list">
              <FileItem
                file={selectedMusic}
                onDelete={handleSelectedFileDelete}
                onPreview={handlePreview}
              />
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <Button
          onClick={handleClear}
          disabled={!selectedVideo && selectedImages.length === 0 && !selectedMusic}
          className="clear-button"
        >
          Clear
        </Button>
        <Button
          type="primary"
          onClick={handleCreate}
          disabled={!selectedVideo && selectedImages.length === 0 && !selectedMusic}
          loading={uploading}
          className="create-button"
        >
          {uploading ? 'Creating' : `Create Media${(() => {
            const count = [selectedVideo, selectedImages.length > 0 ? 1 : null, selectedMusic].filter(f => f !== null).length
            return count > 0 ? ` (${count})` : ''
          })()}`}
        </Button>
      </div>

      {/* Bottom Drawer for File Selection */}
      <Drawer
        title={getDrawerTitle()}
        placement="bottom"
        open={drawerOpen}
        onClose={() => {
          // Stop any playing drawer audio when closing
          drawerAudioElements.forEach((audio) => {
            audio.pause()
            audio.currentTime = 0
          })
          setDrawerAudioPlaying(null)
          setDrawerOpen(false)
        }}
        height="80%"
      >
        {renderDrawerContent()}
      </Drawer>

      {/* Preview Modal */}
      <Modal
        open={previewOpen}
        title={previewFile ? `${previewFile.type === 'image' ? 'Image' : previewFile.type === 'video' ? 'Video' : 'Music'} Preview` : 'Preview'}
        footer={null}
        onCancel={handlePreviewClose}
        centered
        width="90%"
        style={{ maxWidth: '600px' }}
        className="preview-modal"
      >
        {previewFile && (
          <div className="preview-content">
            <>
              {previewFile.type === 'image' ? (
                <img
                  src={getPreviewUrl(previewFile)}
                  alt={previewFile.name}
                  style={{
                    width: '100%',
                    height: 'auto',
                    maxHeight: '70vh',
                    objectFit: 'contain',
                    borderRadius: '8px'
                  }}
                />
              ) : previewFile.type === 'video' ? (
                <video
                  src={getPreviewUrl(previewFile)}
                  controls
                  style={{
                    width: '100%',
                    height: 'auto',
                    maxHeight: '70vh',
                    borderRadius: '8px'
                  }}
                />
              ) : previewFile.type === 'music' ? (
                <div className="music-player-preview">
                  {/* Music Info Header */}
                  <div className="music-info-header">
                    <div className="music-title">{previewFile.name}</div>
                  </div>

                  {/* Progress Section - Live Display */}
                  <div className="progress-section">
                    <div className="progress-track">
                      <div
                        className="progress-fill"
                        style={{ width: `${audioDuration > 0 ? (audioCurrentTime / audioDuration) * 100 : 0}%` }}
                      ></div>
                      <div
                        className="progress-thumb"
                        style={{ left: `${audioDuration > 0 ? (audioCurrentTime / audioDuration) * 100 : 0}%` }}
                      ></div>
                    </div>
                    <div className="time-row">
                      <span className="current-time">{formatTime(audioCurrentTime)}</span>
                      <span className="total-time">{formatTime(audioDuration)}</span>
                    </div>
                  </div>

                  {/* Control Panel - Live Controls */}
                  <div className="music-control-panel">
                    <button
                      className="control-btn seek-btn"
                      onClick={() => seekAudio(-10)}
                      title="Seek backward 10s"
                    >
                      <svg className="control-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        <path d="M5 15V19" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        <path d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523 10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </button>

                    <button
                      className="control-btn play-btn"
                      onClick={toggleAudioPlayback}
                      title={audioPlaying ? "Pause" : "Play"}
                    >
                      {audioPlaying ? (
                        <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                          <path fillRule="evenodd" d="M6.75 5.25a.75.75 0 01.75-.75H9a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H7.5a.75.75 0 01-.75-.75V5.25zm7.5 0A.75.75 0 0115 4.5h1.5a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H15a.75.75 0 01-.75-.75V5.25z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                          <path fillRule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>

                    <button
                      className="control-btn seek-btn"
                      onClick={() => seekAudio(10)}
                      title="Seek forward 10s"
                    >
                      <svg className="control-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        <path d="M13 15V19" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        <path d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18 19H17C16.4477 19 16 18.5523 16 18Z" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </button>
                  </div>
                </div>
              ) : null}
            </>
            <div className="preview-info">
              <p><strong>File name:</strong> {previewFile.name}</p>
              <p><strong>File size:</strong> {formatFileSize(previewFile.size)}</p>
              <p><strong>File type:</strong> {previewFile.type}</p>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Media 