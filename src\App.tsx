import { App as AntApp, Tabs } from 'antd'
import { Material, Media, Preview } from './components'
import './App.css'

function App() {
  const items = [
    {
      key: '1',
      label: 'Material',
      children: <Material />
    },
    {
      key: '2', 
      label: 'Media',
      children: <Media />
    },
    {
      key: '3',
      label: 'Preview', 
      children: <Preview />
    }
  ]

  return (
    <AntApp>
      <div className="app-container">
        <div className="app-content">
          <Tabs 
            items={items}
            defaultActiveKey="1" 
            centered
            size="large"
            className="app-tabs"
          />
        </div>
      </div>
    </AntApp>
  )
}

export default App
