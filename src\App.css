/* App Container */
.app-container {
  width: 100%;
  max-width: 768px; /* Tablet max width */
  min-height: 100vh;
  margin: 0 auto;
  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}



/* App Content */
.app-content {
  flex: 1;
  padding: 0;
  background-color: #ffffff;
}

/* Tabs Styling */
.app-tabs {
  height: 100%;
}

.app-tabs .ant-tabs-nav {
  margin: 0;
  padding: 0 16px;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 99;
}

.app-tabs .ant-tabs-tab {
  font-weight: 500;
  font-size: 16px;
  margin: 0 !important;
  flex: 1;
  display: flex;
  justify-content: center;
}

.app-tabs .ant-tabs-content-holder {
  padding: 20px 16px;
}

/* Mobile Optimizations */
@media (max-width: 480px) {
  .app-container {
    max-width: 100%;
    box-shadow: none;
  }
  
  .app-tabs .ant-tabs-nav {
    padding: 0 12px;
    top: 0;
  }
  
  .app-tabs .ant-tabs-tab {
    font-size: 14px;
  }
  
  .app-tabs .ant-tabs-content-holder {
    padding: 16px 12px;
  }
}

/* Prevent tab switching animation glitch */
.app-tabs .ant-tabs-nav-wrap {
  justify-content: center;
}

.app-tabs .ant-tabs-nav-list {
  width: 100%;
  display: flex;
  justify-content: center;
}

